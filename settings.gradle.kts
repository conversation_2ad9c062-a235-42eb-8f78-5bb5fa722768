pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.name = "NoteMess"
includeBuild("build-logic")

include(":app")

include(":core:domain")
include(":core:data")

include(":core:feature")
include(":core:ui")

include(":firestore-collection:user")
include(":firestore-collection:task")

include(":feature:auth:auth-api")
include(":feature:auth:auth-impl")
include(":feature:main:main-api")
include(":feature:main:main-impl")
include(":feature:calendar:calendar-api")
include(":feature:calendar:calendar-impl")
include(":feature:account:account-api")
include(":feature:account:account-db")
include(":feature:account:account-impl")
include(":feature:task:common")
include(":feature:task:details:task-api")
include(":feature:task:details:task-db")
include(":feature:task:details:task-impl")
include(":feature:task:member:member-api")
include(":feature:task:member:member-db")
include(":feature:task:member:member-impl")
include(":feature:task:attachment:attachment-api")
include(":feature:task:attachment:attachment-db")
include(":feature:task:attachment:attachment-impl")
include(":feature:settings:settings-api")
include(":feature:settings:settings-impl")
include(":feature:user:user-api")
include(":feature:user:user-db")
include(":feature:user:user-impl")
include(":feature:contact:contact-api")
include(":feature:contact:contact-db")
include(":feature:contact:contact-impl")

include(":system:file-api")
include(":system:file-impl")
