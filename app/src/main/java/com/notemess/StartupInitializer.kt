package com.notemess

import android.content.Context
import androidx.startup.Initializer
import com.notemess.core.data.coreDataModule
import com.notemess.feature.account.accountFeatureModule
import com.notemess.feature.auth.authFeatureModule
import com.notemess.feature.calendar.calendarFeatureModule
import com.notemess.feature.contact.contactFeatureModule
import com.notemess.feature.main.mainFeatureModule
import com.notemess.feature.settings.settingsFeatureModule
import com.notemess.feature.task.attachment.taskAttachmentFeatureModule
import com.notemess.feature.task.member.taskMemberFeatureModule
import com.notemess.feature.task.taskFeatureModule
import com.notemess.feature.user.userFeatureModule
import com.notemess.system.file.fileSystemModule
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import timber.log.Timber

class StartupInitializer : Initializer<Context> {

    override fun create(context: Context): Context {
        Timber.plant(Timber.DebugTree())

        startKoin {
            androidContext(context)
            androidLogger()

            modules(
                appModule,
                coreDataModule,
                fileSystemModule,
                accountFeatureModule,
                authFeatureModule,
                mainFeatureModule,
                calendarFeatureModule,
                contactFeatureModule,
                taskMemberFeatureModule,
                taskAttachmentFeatureModule,
                taskFeatureModule,
                userFeatureModule,
                settingsFeatureModule
            )
        }

        return context
    }

    override fun dependencies(): List<Class<out Initializer<*>?>?> {
        return emptyList()
    }
}
