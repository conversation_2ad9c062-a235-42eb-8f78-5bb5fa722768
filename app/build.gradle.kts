plugins {
    id(libs.plugins.notemess.application.asProvider().get().pluginId)
    id(libs.plugins.notemess.application.compose.get().pluginId)

    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.google.services)
    alias(libs.plugins.kotlin.ksp)
}

android {
    namespace = "com.notemess"

    defaultConfig {
        applicationId = "com.notemess"
        versionCode = 1
        versionName = "1.0"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    buildFeatures {
        buildConfig = true
    }

    applicationVariants.all {
        val variant = this
        variant.outputs
            .map { it as com.android.build.gradle.internal.api.BaseVariantOutputImpl }
            .forEach { output ->
                output.outputFileName = "notemess_${variant.versionCode}.apk"
            }
    }
}

ksp {
    arg("room.schemaLocation", "$projectDir/schemas")
    arg("room.generateKotlin", "true")
}

dependencies {
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.serialization.json)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.core.splashscreen)
    implementation(libs.androidx.activity.compose)

    implementation(libs.androidx.startup)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.auth)
    implementation(libs.firebase.firestore)
    implementation(libs.firebase.messaging)

    implementation(libs.koin.android)
    implementation(libs.koin.compose)

    implementation(libs.decompose.extensions.compose)

    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    implementation(projects.core.data)
    implementation(projects.core.domain)
    implementation(projects.core.ui)

    implementation(projects.firestoreCollection.user)
    implementation(projects.firestoreCollection.task)

    implementation(projects.feature.account.accountDb)
    implementation(projects.feature.account.accountImpl)
    implementation(projects.feature.auth.authImpl)
    implementation(projects.feature.main.mainImpl)
    implementation(projects.feature.calendar.calendarImpl)
    implementation(projects.feature.user.userDb)
    implementation(projects.feature.user.userImpl)
    implementation(projects.feature.contact.contactDb)
    implementation(projects.feature.contact.contactImpl)
    implementation(projects.feature.task.member.memberDb)
    implementation(projects.feature.task.member.memberImpl)
    implementation(projects.feature.task.attachment.attachmentDb)
    implementation(projects.feature.task.attachment.attachmentImpl)
    implementation(projects.feature.task.details.taskDb)
    implementation(projects.feature.task.details.taskImpl)
    implementation(projects.feature.settings.settingsImpl)
    implementation(projects.system.fileImpl)
}
