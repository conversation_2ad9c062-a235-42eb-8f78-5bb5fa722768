package com.notemess.feature.task.attachment.entity

import androidx.room.Embedded
import androidx.room.Entity
import com.notemess.feature.task.attachment.model.TaskAttachmentId
import com.notemess.feature.task.attachment.model.TaskAttachmentType
import com.notemess.feature.task.common.TaskId
import com.notemess.feature.user.UserId

@Entity(tableName = "task_attachments")
data class DatabaseTaskAttachmentEntity(
    val id: TaskAttachmentId,
    val taskId: TaskId,
    val type: TaskAttachmentType,
    val size: Long,
    val name: String,
    val remoteUrl: String?,
    val localPath: String?,
    val createdAt: Long,
    val createdByUserId: UserId,

    @Embedded(prefix = "image_info_")
    val imageInfo: ImageInfo?
) {

    data class ImageInfo(
        val thumbnailRemoteUrl: String,
        val thumbnailLocalPath: String?,
    )
}