package com.notemess.feature.task.attachment.data

import com.domain.Data
import com.notemess.core.data.DefaultDataImpl
import com.notemess.feature.task.attachment.data.source.LocalTaskAttachmentDataSource
import com.notemess.feature.task.attachment.data.source.RemoteTaskAttachmentDataSource
import com.notemess.feature.task.attachment.domain.TaskAttachmentMutation
import com.notemess.feature.task.attachment.domain.TaskAttachmentRepository
import com.notemess.feature.task.attachment.model.TaskAttachment
import com.notemess.feature.task.common.TaskId

internal class TaskAttachmentRepositoryImpl(
    private val localTaskAttachmentDataSource: LocalTaskAttachmentDataSource,
    private val remoteTaskAttachmentDataSource: RemoteTaskAttachmentDataSource
) : TaskAttachmentRepository {

    override fun getTaskAttachmentListByTaskId(taskId: TaskId): Data<List<TaskAttachment>> {
        return DefaultDataImpl(
            loadRemote = { remoteTaskAttachmentDataSource.getTaskAttachmentListByTaskId(taskId) },
            loadLocal = { localTaskAttachmentDataSource.getTaskAttachmentListByTaskId(taskId) },
            updateLocal = { taskAttachmentList -> localTaskAttachmentDataSource.insertOrReplaceTaskAttachments(taskAttachmentList) },
            observeLocal = { localTaskAttachmentDataSource.observeTaskAttachmentListByTaskId(taskId) }
        )
    }

    override suspend fun applyMutation(mutation: TaskAttachmentMutation): Result<Unit> {
        return localTaskAttachmentDataSource.applyMutationTaskAttachment(mutation)
            .mapCatching { remoteTaskAttachmentDataSource.applyMutationTaskAttachment(mutation) }
    }
}
