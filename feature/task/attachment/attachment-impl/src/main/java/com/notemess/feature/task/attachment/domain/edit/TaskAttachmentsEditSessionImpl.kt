package com.notemess.feature.task.attachment.domain.edit

import com.notemess.core.data.localFirstAndThenRemote
import com.notemess.feature.account.AccountRepository
import com.notemess.feature.task.attachment.domain.TaskAttachmentIdFactory
import com.notemess.feature.task.attachment.domain.TaskAttachmentMutation
import com.notemess.feature.task.attachment.domain.TaskAttachmentRepository
import com.notemess.feature.task.attachment.model.TaskAttachment
import com.notemess.feature.task.attachment.model.TaskAttachmentId
import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.common.edit.TaskEditSession
import com.notemess.feature.user.UserId
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

internal class TaskAttachmentsEditSessionImpl(
    private val taskId: TaskId,
    private val accountRepository: AccountRepository,
    private val taskAttachmentRepository: TaskAttachmentRepository,
    private val taskAttachmentIdFactory: TaskAttachmentIdFactory,
) : TaskAttachmentsEditSession {

    private val mutations = MutableStateFlow(emptyMap<TaskAttachmentId, TaskAttachmentMutation>())
    private val committingMutex = Mutex()

    override fun previewData(): Flow<Result<List<TaskAttachment>>> {
        return combine(
            taskAttachmentListByTaskId(),
            mutations
        ) { taskAttachmentResult, mutations ->
            taskAttachmentResult.map { taskAttachments -> applyMutationTo(taskAttachments, mutations) }
        }.filterNot { committingMutex.isLocked }
    }

    override fun mutate(mutation: TaskAttachmentMutation) {
        updateMutation { set(getKeyForMutation(mutation), mutation) }
    }

    override suspend fun commit(): Result<Unit> {
        return coroutineScope {
            committingMutex.withLock {
                val capturedMutations = mutations.value
                mutations.value = emptyMap()

                val mutationResults = capturedMutations.values.map { mutation ->
                    async { taskAttachmentRepository.applyMutation(mutation) }
                }.awaitAll()

                mutationResults.forEach { result ->
                    result.onFailure { error ->
                        mutations.value = capturedMutations
                        return@coroutineScope Result.failure(error)
                    }
                }

                Result.success(Unit)
            }
        }
    }

    override fun clear() {
        mutations.value = emptyMap()
    }

    private suspend fun TaskAttachmentMutation.toTaskAttachmentOrNull(): TaskAttachment? {
        return when (this) {
            is TaskAttachmentMutation.Add -> TaskAttachment(
                id = taskAttachmentIdFactory.create(),
                taskId = taskId,
                type = type,
                size = size,
                filename = filename,
                remoteUrl = null,
                localPath = localPath,
                imageInfo = imageInfo,
                createdAt = System.currentTimeMillis(),
                createdByUserId = accountRepository.getAccount()?.id?.let(::UserId) ?: error("Account not found")
            )

            is TaskAttachmentMutation.Remove -> null
        }
    }

    private fun taskAttachmentListByTaskId(): Flow<Result<List<TaskAttachment>>> {
        return taskAttachmentRepository.getTaskAttachmentListByTaskId(taskId).localFirstAndThenRemote()
            .map { taskAttachmentsResult ->
                taskAttachmentsResult.map { taskAttachmentsData ->
                    taskAttachmentsData.value
                }
            }
    }

    private fun updateMutation(action: MutableMap<TaskAttachmentId, TaskAttachmentMutation>.() -> Unit) {
        mutations.value = mutations.value.toMutableMap().apply(action)
    }

    private fun getKeyForMutation(mutation: TaskAttachmentMutation): TaskAttachmentId {
        return when (mutation) {
            is TaskAttachmentMutation.Add -> taskAttachmentIdFactory.create()
            is TaskAttachmentMutation.Remove -> mutation.attachmentId
        }
    }

    private suspend fun applyMutationTo(
        attachments: List<TaskAttachment>,
        mutations: Map<TaskAttachmentId, TaskAttachmentMutation>
    ): List<TaskAttachment> {
        val mutationAttachments = mutations.values
            .mapNotNull { mutation -> mutation.toTaskAttachmentOrNull() }

        return (attachments + mutationAttachments)
            .filterNot { attachment -> mutations[attachment.id] is TaskAttachmentMutation.Remove }
            .distinctBy { it.id }
    }

    class Factory(
        private val accountRepository: AccountRepository,
        private val taskAttachmentRepository: TaskAttachmentRepository,
        private val taskAttachmentIdFactory: TaskAttachmentIdFactory,
    ) : TaskEditSession.Factory {

        override val typeOfSession = TaskAttachmentsEditSession::class

        override fun create(taskId: TaskId): TaskAttachmentsEditSession {
            return TaskAttachmentsEditSessionImpl(taskId, accountRepository, taskAttachmentRepository, taskAttachmentIdFactory)
        }
    }
}