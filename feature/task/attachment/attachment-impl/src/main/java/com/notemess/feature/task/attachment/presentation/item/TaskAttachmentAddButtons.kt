package com.notemess.feature.task.attachment.presentation.item

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import com.notemess.core.ui.foundation.AppTheme

@Composable
internal fun TaskAttachmentAddButtons(
    modifier: Modifier = Modifier,
    onAddImageClicked: () -> Unit,
    onAddDocumentClicked: () -> Unit,
    onAddFileClicked: () -> Unit
) {
    Column(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Button(
            onClick = onAddImageClicked,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Изображение")
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedButton(
                onClick = onAddDocumentClicked,
                modifier = Modifier.weight(1f)
            ) {
                Text("Документ")
            }

            OutlinedButton(
                onClick = onAddFileClicked,
                modifier = Modifier.weight(1f)
            ) {
                Text("Файл")
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun TaskAttachmentAddButtonsPreview() = AppTheme {
    TaskAttachmentAddButtons(
        onAddImageClicked = {},
        onAddDocumentClicked = {},
        onAddFileClicked = {}
    )
}
