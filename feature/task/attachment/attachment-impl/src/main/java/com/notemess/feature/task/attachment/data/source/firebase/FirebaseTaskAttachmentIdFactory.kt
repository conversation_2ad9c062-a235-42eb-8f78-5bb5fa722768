package com.notemess.feature.task.attachment.data.source.firebase

import com.notemess.feature.task.attachment.domain.TaskAttachmentIdFactory
import com.notemess.feature.task.attachment.model.TaskAttachmentId
import java.util.UUID

internal class FirebaseTaskAttachmentIdFactory : TaskAttachmentIdFactory {

    override fun create(): TaskAttachmentId {
        return TaskAttachmentId(UUID.randomUUID().toString())
    }
}
