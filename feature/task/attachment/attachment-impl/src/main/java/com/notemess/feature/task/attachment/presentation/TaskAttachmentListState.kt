package com.notemess.feature.task.attachment.presentation

import androidx.compose.runtime.Immutable
import com.notemess.feature.task.attachment.presentation.item.TaskAttachmentListItem
import kotlinx.collections.immutable.ImmutableList

@Immutable
internal sealed interface TaskAttachmentListState {

    data class Content(
        val items: ImmutableList<TaskAttachmentListItem>
    ) : TaskAttachmentListState

    data object Loading : TaskAttachmentListState

    data class Error(val throwable: Throwable) : TaskAttachmentListState

    companion object {
        val Default: TaskAttachmentListState = Loading
    }
}
