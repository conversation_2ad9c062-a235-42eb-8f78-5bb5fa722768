package com.notemess.feature.task.attachment.data.source

import com.notemess.feature.task.attachment.domain.TaskAttachmentMutation
import com.notemess.feature.task.attachment.model.TaskAttachment
import com.notemess.feature.task.common.TaskId

internal interface RemoteTaskAttachmentDataSource {

    suspend fun getTaskAttachmentListByTaskId(taskId: TaskId): Result<List<TaskAttachment>>

    suspend fun applyMutationTaskAttachment(mutation: TaskAttachmentMutation): Result<Unit>
}
