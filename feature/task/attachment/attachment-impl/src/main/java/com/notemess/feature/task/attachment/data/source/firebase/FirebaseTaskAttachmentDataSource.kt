package com.notemess.feature.task.attachment.data.source.firebase

import com.notemess.feature.task.attachment.data.source.RemoteTaskAttachmentDataSource
import com.notemess.feature.task.attachment.domain.TaskAttachmentMutation
import com.notemess.feature.task.attachment.model.TaskAttachment
import com.notemess.feature.task.common.TaskId

internal class FirebaseTaskAttachmentDataSource : RemoteTaskAttachmentDataSource {

    override suspend fun getTaskAttachmentListByTaskId(taskId: TaskId): Result<List<TaskAttachment>> {
        // TODO: Implement Firebase Firestore integration
        return Result.success(emptyList())
    }

    override suspend fun applyMutationTaskAttachment(mutation: TaskAttachmentMutation): Result<Unit> {
        // TODO: Implement Firebase Firestore mutation
        return Result.success(Unit)
    }
}
