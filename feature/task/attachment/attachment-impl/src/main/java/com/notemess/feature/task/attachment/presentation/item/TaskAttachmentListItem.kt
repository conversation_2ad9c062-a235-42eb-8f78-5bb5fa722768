package com.notemess.feature.task.attachment.presentation.item

import androidx.compose.runtime.Immutable
import com.notemess.feature.task.attachment.model.TaskAttachmentId
import com.notemess.feature.task.attachment.model.TaskAttachmentType

@Immutable
internal data class TaskAttachmentListItem(
    val id: TaskAttachmentId,
    val filename: String,
    val type: TaskAttachmentType,
    val size: Long,
    val localPath: String?
)
