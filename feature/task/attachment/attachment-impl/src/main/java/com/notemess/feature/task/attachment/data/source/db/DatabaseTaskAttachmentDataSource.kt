package com.notemess.feature.task.attachment.data.source.db

import com.notemess.feature.task.attachment.DatabaseTaskAttachmentDao
import com.notemess.feature.task.attachment.DatabaseTaskAttachmentMapper
import com.notemess.feature.task.attachment.data.source.LocalTaskAttachmentDataSource
import com.notemess.feature.task.attachment.domain.TaskAttachmentMutation
import com.notemess.feature.task.attachment.model.TaskAttachment
import com.notemess.feature.task.common.TaskId
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

internal class DatabaseTaskAttachmentDataSource(
    private val dao: DatabaseTaskAttachmentDao
) : LocalTaskAttachmentDataSource {

    override suspend fun getTaskAttachmentListByTaskId(taskId: TaskId): Result<List<TaskAttachment>> {
        return runCatching {
            dao.getTaskAttachmentsByTaskId(taskId).map(DatabaseTaskAttachmentMapper::toDomain)
        }
    }

    override fun observeTaskAttachmentListByTaskId(taskId: TaskId): Flow<List<TaskAttachment>> {
        return dao.observeTaskAttachmentsByTaskId(taskId)
            .map { entities -> entities.map(DatabaseTaskAttachmentMapper::toDomain) }
    }

    override suspend fun insertOrReplaceTaskAttachments(taskAttachments: List<TaskAttachment>): Result<Unit> {
        return runCatching {
            val entities = taskAttachments.map(DatabaseTaskAttachmentMapper::toDatabaseEntity)
            dao.insertOrReplaceTaskAttachments(entities)
        }
    }

    override suspend fun applyMutationTaskAttachment(mutation: TaskAttachmentMutation): Result<Unit> {
        return runCatching {
            when (mutation) {
                is TaskAttachmentMutation.Add -> {
                    // For Add mutation, we don't need to do anything in local DB
                    // The attachment will be added when the remote operation succeeds
                }
                is TaskAttachmentMutation.Remove -> {
                    // For Remove mutation, we can delete immediately from local DB
                    dao.deleteTaskAttachmentsByTaskId(mutation.taskId)
                }
            }
        }
    }
}
