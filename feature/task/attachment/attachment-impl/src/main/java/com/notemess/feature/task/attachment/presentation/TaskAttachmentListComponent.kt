package com.notemess.feature.task.attachment.presentation

import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.value.Value
import com.notemess.core.feature.FeatureComponent
import com.notemess.core.ui.feature.router.ExternalFeatureChild
import com.notemess.feature.task.attachment.presentation.item.TaskAttachmentListItem
import kotlinx.coroutines.flow.StateFlow

internal interface TaskAttachmentListComponent : FeatureComponent {

    val state: StateFlow<TaskAttachmentListState>
    val childStack: Value<ChildStack<*, Child>>

    fun onItemClicked(item: TaskAttachmentListItem)

    fun onAddImageClicked()

    fun onAddDocumentClicked()

    fun onAddFileClicked()

    sealed interface Child {
        data object None : Child

        data class ExternalFeature(
            override val featureId: String,
            override val featureComponent: FeatureComponent
        ) : Child, ExternalFeatureChild
    }
}
