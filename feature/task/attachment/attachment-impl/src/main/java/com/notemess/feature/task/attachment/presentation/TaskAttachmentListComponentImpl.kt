package com.notemess.feature.task.attachment.presentation

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnDestroy
import com.notemess.core.ui.decompose.componentScope
import com.notemess.core.ui.decompose.host
import com.notemess.core.ui.preview.previewDataOrDefault
import com.notemess.core.ui.preview.previewDataOrNull
import com.notemess.feature.task.attachment.TaskAttachmentsScreenFeature
import com.notemess.feature.task.attachment.domain.TaskAttachmentMutation
import com.notemess.feature.task.attachment.domain.edit.TaskAttachmentsEditSession
import com.notemess.feature.task.attachment.model.TaskAttachmentType
import com.notemess.feature.task.attachment.presentation.item.TaskAttachmentListItem
import com.notemess.feature.task.common.edit.TaskEditManager
import com.notemess.system.file.FilePickerUseCase
import com.notemess.system.file.FileType
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

internal class TaskAttachmentListComponentImpl(
    context: ComponentContext,
    private val params: TaskAttachmentsScreenFeature.Params,
    private val filePickerUseCase: FilePickerUseCase,
    private val taskEditManager: TaskEditManager
) : ComponentContext by context,
    TaskAttachmentListComponent {

    private val host = host<Configuration, TaskAttachmentListComponent.Child> {
        handleBackButton = true
        initialConfiguration = Configuration.None
        serializer = Configuration.serializer()
    }

    private val mutableState = MutableStateFlow(context.previewDataOrDefault(TaskAttachmentListState.Default))
    override val state = mutableState.asStateFlow()
    override val childStack = host.childStack

    private val editSession by lazy {
        taskEditManager.getSession(params.taskId, TaskAttachmentsEditSession::class)
    }

    init {
        if (context.previewDataOrNull<TaskAttachmentListState>() == null) {
            componentScope.launch { observeTaskAttachmentList() }
        }

        lifecycle.doOnDestroy { editSession.clear() }
    }

    override fun onItemClicked(item: TaskAttachmentListItem) {
        componentScope.launch {
            val mutation = TaskAttachmentMutation.Remove(
                taskId = params.taskId,
                attachmentId = item.id
            )
            editSession.mutate(mutation)
        }
    }

    override fun onAddImageClicked() {
        componentScope.launch {
            filePickerUseCase.pickImage().onSuccess { fileInfo ->
                fileInfo?.let { addAttachment(it.toTaskAttachmentType(), it.name, it.size, it.localPath) }
            }
        }
    }

    override fun onAddDocumentClicked() {
        componentScope.launch {
            filePickerUseCase.pickDocument().onSuccess { fileInfo ->
                fileInfo?.let { addAttachment(TaskAttachmentType.Document, it.name, it.size, it.localPath) }
            }
        }
    }

    override fun onAddFileClicked() {
        componentScope.launch {
            filePickerUseCase.pickFile().onSuccess { fileInfo ->
                fileInfo?.let { addAttachment(TaskAttachmentType.File, it.name, it.size, it.localPath) }
            }
        }
    }

    private suspend fun addAttachment(type: TaskAttachmentType, filename: String, size: Long, localPath: String) {
        val mutation = TaskAttachmentMutation.Add(
            taskId = params.taskId,
            type = type,
            size = size,
            filename = filename,
            localPath = localPath,
            imageInfo = null // TODO: Generate thumbnail for images
        )
        editSession.mutate(mutation)
    }

    private suspend fun observeTaskAttachmentList() {
        editSession.previewData().collect { attachmentListResult ->
            attachmentListResult.fold(
                onSuccess = { attachmentList ->
                    mutableState.update {
                        TaskAttachmentListState.Content(
                            items = TaskAttachmentListUiMapper.toUIItems(attachmentList).toPersistentList()
                        )
                    }
                },
                onFailure = { error ->
                    mutableState.update {
                        TaskAttachmentListState.Error(error)
                    }
                }
            )
        }
    }

    private fun com.notemess.system.file.FileInfo.toTaskAttachmentType(): TaskAttachmentType {
        return when (type) {
            FileType.Image -> TaskAttachmentType.Image
            FileType.Document -> TaskAttachmentType.Document
            FileType.Unknown -> TaskAttachmentType.Unknown
        }
    }

    @Serializable
    sealed interface Configuration {
        data object None : Configuration
    }
}
