package com.notemess.feature.task.attachment.presentation

import com.notemess.feature.task.attachment.model.TaskAttachment
import com.notemess.feature.task.attachment.presentation.item.TaskAttachmentListItem

internal object TaskAttachmentListUiMapper {

    fun toUIItems(attachments: List<TaskAttachment>): List<TaskAttachmentListItem> {
        return attachments.map { attachment ->
            TaskAttachmentListItem(
                id = attachment.id,
                filename = attachment.filename,
                type = attachment.type,
                size = attachment.size,
                localPath = attachment.localPath
            )
        }
    }
}
