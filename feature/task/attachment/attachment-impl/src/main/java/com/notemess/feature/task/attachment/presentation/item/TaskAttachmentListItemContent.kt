package com.notemess.feature.task.attachment.presentation.item

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.InsertDriveFile
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.notemess.core.ui.foundation.AppTheme
import com.notemess.feature.task.attachment.model.TaskAttachmentId
import com.notemess.feature.task.attachment.model.TaskAttachmentType
import kotlin.math.pow

@Composable
internal fun TaskAttachmentListItemContent(
    modifier: Modifier = Modifier,
    item: TaskAttachmentListItem,
    onClick: () -> Unit
) {
    Card(
        modifier = modifier.padding(horizontal = 16.dp, vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = when (item.type) {
                    TaskAttachmentType.Image -> Icons.Default.Image
                    TaskAttachmentType.Document -> Icons.Default.Description
                    TaskAttachmentType.File -> Icons.Default.InsertDriveFile
                    TaskAttachmentType.Unknown -> Icons.Default.InsertDriveFile
                },
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = item.filename,
                    color = AppTheme.colorScheme.textPrimary,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = formatFileSize(item.size),
                    color = AppTheme.colorScheme.textSecondary,
                    fontSize = 14.sp,
                    maxLines = 1
                )
            }

            IconButton(
                onClick = onClick
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "Удалить",
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

private fun formatFileSize(bytes: Long): String {
    if (bytes < 1024) return "$bytes B"
    val exp = (kotlin.math.ln(bytes.toDouble()) / kotlin.math.ln(1024.0)).toInt()
    val pre = "KMGTPE"[exp - 1]
    return String.format("%.1f %sB", bytes / 1024.0.pow(exp.toDouble()), pre)
}

@PreviewLightDark
@Composable
private fun TaskAttachmentListItemContentPreview() = AppTheme {
    TaskAttachmentListItemContent(
        modifier = Modifier.fillMaxWidth(),
        item = TaskAttachmentListItem(
            id = TaskAttachmentId("1"),
            filename = "IMG_3932.jpeg",
            type = TaskAttachmentType.Image,
            size = 1024 * 1024,
            localPath = "/path/to/image.jpg"
        ),
        onClick = {}
    )
}
