package com.notemess.feature.task.attachment

import com.notemess.core.feature.FeatureCallbacks
import com.notemess.core.feature.FeatureComponent
import com.notemess.core.feature.FeatureContent
import com.notemess.core.feature.FeatureDependencies
import com.notemess.core.feature.FeatureEntryPoint
import com.notemess.core.ui.decompose.DecomposeComponentFactory
import com.notemess.core.ui.decompose.DecomposeFeatureDependencies
import com.notemess.core.ui.decompose.createComponent
import com.notemess.feature.task.attachment.presentation.TaskAttachmentListComponent
import com.notemess.feature.task.attachment.presentation.TaskAttachmentListContent

internal class TaskAttachmentListFeatureEntryPoint(
    private val decomposeComponentFactory: DecomposeComponentFactory
) : FeatureEntryPoint<TaskAttachmentsScreenFeature> {

    override val id = TaskAttachmentsScreenFeature.ID

    override fun featureClass() = TaskAttachmentsScreenFeature::class

    override fun featureSerializer() = TaskAttachmentsScreenFeature.serializer()

    override fun <Params> createComponent(
        dependencies: FeatureDependencies,
        params: Params?,
        callbacks: FeatureCallbacks?
    ): FeatureComponent {
        val decomposeFeatureDependencies = dependencies as? DecomposeFeatureDependencies ?: error(
            "Expected DecomposeFeatureDependencies, but got $dependencies"
        )

        return decomposeComponentFactory.createComponent<TaskAttachmentListComponent>(
            context = decomposeFeatureDependencies.context,
            params = params,
            callbacks = callbacks
        )
    }

    override fun createContent(component: FeatureComponent): FeatureContent {
        val taskAttachmentComponent = component as? TaskAttachmentListComponent ?: error(
            "Expected TaskAttachmentListComponent, but got $component"
        )

        return TaskAttachmentListContent(
            component = taskAttachmentComponent
        )
    }
}
