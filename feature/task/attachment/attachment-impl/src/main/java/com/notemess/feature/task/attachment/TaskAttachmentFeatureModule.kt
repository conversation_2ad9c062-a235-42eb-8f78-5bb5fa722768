package com.notemess.feature.task.attachment

import com.notemess.core.feature.FeatureEntryPoint
import com.notemess.feature.task.attachment.data.TaskAttachmentRepositoryImpl
import com.notemess.feature.task.attachment.data.source.LocalTaskAttachmentDataSource
import com.notemess.feature.task.attachment.data.source.RemoteTaskAttachmentDataSource
import com.notemess.feature.task.attachment.data.source.db.DatabaseTaskAttachmentDataSource
import com.notemess.feature.task.attachment.data.source.firebase.FirebaseTaskAttachmentDataSource
import com.notemess.feature.task.attachment.data.source.firebase.FirebaseTaskAttachmentIdFactory
import com.notemess.feature.task.attachment.domain.TaskAttachmentIdFactory
import com.notemess.feature.task.attachment.domain.TaskAttachmentRepository
import com.notemess.feature.task.attachment.domain.edit.TaskAttachmentsEditSessionImpl
import com.notemess.feature.task.attachment.presentation.TaskAttachmentListComponent
import com.notemess.feature.task.attachment.presentation.TaskAttachmentListComponentImpl
import com.notemess.feature.task.attachment.usecase.GetAttachmentsByTaskIdUseCase
import com.notemess.feature.task.common.edit.TaskEditSession
import org.koin.core.module.Module
import org.koin.core.module.dsl.factoryOf
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

public val taskAttachmentFeatureModule: Module = module {
    factoryOf(::TaskAttachmentListFeatureEntryPoint).onOptions { bind<FeatureEntryPoint<*>>() }

    singleOf(::FirebaseTaskAttachmentIdFactory).bind<TaskAttachmentIdFactory>()
    singleOf(::FirebaseTaskAttachmentDataSource).bind<RemoteTaskAttachmentDataSource>()
    singleOf(::DatabaseTaskAttachmentDataSource).bind<LocalTaskAttachmentDataSource>()
    singleOf(::TaskAttachmentRepositoryImpl).bind<TaskAttachmentRepository>()

    factoryOf(TaskAttachmentsEditSessionImpl::Factory).bind<TaskEditSession.Factory>()

    factoryOf(::GetAttachmentsByTaskIdUseCaseImpl).bind<GetAttachmentsByTaskIdUseCase>()
    factoryOf(::TaskAttachmentListComponentImpl).bind<TaskAttachmentListComponent>()
}
