package com.notemess.feature.task.attachment.data.source

import com.notemess.feature.task.attachment.domain.TaskAttachmentMutation
import com.notemess.feature.task.attachment.model.TaskAttachment
import com.notemess.feature.task.common.TaskId
import kotlinx.coroutines.flow.Flow

internal interface LocalTaskAttachmentDataSource {

    suspend fun getTaskAttachmentListByTaskId(taskId: TaskId): Result<List<TaskAttachment>>

    fun observeTaskAttachmentListByTaskId(taskId: TaskId): Flow<List<TaskAttachment>>

    suspend fun insertOrReplaceTaskAttachments(taskAttachments: List<TaskAttachment>): Result<Unit>

    suspend fun applyMutationTaskAttachment(mutation: TaskAttachmentMutation): Result<Unit>
}
