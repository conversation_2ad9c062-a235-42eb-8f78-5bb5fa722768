package com.notemess.feature.task.attachment.presentation

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.arkivanov.decompose.extensions.compose.stack.Children
import com.arkivanov.decompose.extensions.compose.stack.animation.stackAnimation
import com.notemess.core.ui.feature.ComposeFeatureContent
import com.notemess.core.ui.feature.ComposeFeatureContentRenderer
import com.notemess.core.ui.foundation.AppTheme
import com.notemess.core.ui.preview.PreviewSandbox
import com.notemess.core.ui.preview.injectPreviewComponent
import com.notemess.feature.task.attachment.TaskAttachmentsScreenFeature
import com.notemess.feature.task.attachment.model.TaskAttachmentId
import com.notemess.feature.task.attachment.model.TaskAttachmentType
import com.notemess.feature.task.attachment.presentation.item.TaskAttachmentListItem
import com.notemess.feature.task.attachment.presentation.item.TaskAttachmentListItemContent
import com.notemess.feature.task.attachment.presentation.item.TaskAttachmentAddButtons
import com.notemess.feature.task.common.TaskId
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf

internal class TaskAttachmentListContent(
    private val component: TaskAttachmentListComponent
) : ComposeFeatureContent {

    @Composable
    override fun Composable(modifier: Modifier) {
        TaskAttachmentListContent(
            modifier = modifier,
            component = component
        )
    }
}

@Composable
private fun TaskAttachmentListContent(
    modifier: Modifier = Modifier,
    component: TaskAttachmentListComponent
) {
    val composeFeatureContentRenderer = koinInject<ComposeFeatureContentRenderer>()
    val state by component.state.collectAsStateWithLifecycle()

    Children(
        stack = component.childStack,
        animation = stackAnimation()
    ) { createdChild ->
        val child = createdChild.instance
        when (child) {
            TaskAttachmentListComponent.Child.None -> {
                AnimatedContent(
                    modifier = modifier,
                    targetState = state,
                    contentKey = { state -> state.javaClass.name }
                ) { state ->
                    when (state) {
                        is TaskAttachmentListState.Content -> {
                            ListContent(
                                items = state.items,
                                onItemClicked = component::onItemClicked,
                                onAddImageClicked = component::onAddImageClicked,
                                onAddDocumentClicked = component::onAddDocumentClicked,
                                onAddFileClicked = component::onAddFileClicked
                            )
                        }

                        is TaskAttachmentListState.Error -> {
                            ErrorContent(error = state.throwable)
                        }

                        TaskAttachmentListState.Loading -> {
                            LoadingContent()
                        }
                    }
                }
            }

            is TaskAttachmentListComponent.Child.ExternalFeature -> {
                composeFeatureContentRenderer.Feature(
                    modifier = Modifier.fillMaxSize(),
                    featureId = child.featureId,
                    component = child.featureComponent
                )
            }
        }
    }
}

@Composable
private fun ListContent(
    modifier: Modifier = Modifier,
    items: ImmutableList<TaskAttachmentListItem>,
    onItemClicked: (TaskAttachmentListItem) -> Unit,
    onAddImageClicked: () -> Unit,
    onAddDocumentClicked: () -> Unit,
    onAddFileClicked: () -> Unit
) {
    Column(modifier) {
        if (items.isEmpty()) {
            EmptyStateContent(
                onAddImageClicked = onAddImageClicked,
                onAddDocumentClicked = onAddDocumentClicked,
                onAddFileClicked = onAddFileClicked
            )
        } else {
            LazyColumn {
                item {
                    TaskAttachmentAddButtons(
                        onAddImageClicked = onAddImageClicked,
                        onAddDocumentClicked = onAddDocumentClicked,
                        onAddFileClicked = onAddFileClicked
                    )
                }

                items(
                    items = items,
                    key = { item -> item.id.value }
                ) { item ->
                    TaskAttachmentListItemContent(
                        modifier = Modifier.fillMaxWidth(),
                        item = item,
                        onClick = { onItemClicked(item) }
                    )
                }
            }
        }
    }
}

@Composable
private fun EmptyStateContent(
    modifier: Modifier = Modifier,
    onAddImageClicked: () -> Unit,
    onAddDocumentClicked: () -> Unit,
    onAddFileClicked: () -> Unit
) {
    Column(modifier) {
        Text(
            text = "Приложений пока не добавлено",
            style = AppTheme.typography.titleMedium
        )
        Text(
            text = "Добавьте нужные вам приложения",
            style = AppTheme.typography.bodyMedium
        )
        TaskAttachmentAddButtons(
            onAddImageClicked = onAddImageClicked,
            onAddDocumentClicked = onAddDocumentClicked,
            onAddFileClicked = onAddFileClicked
        )
    }
}

@Composable
private fun LoadingContent(modifier: Modifier = Modifier) {
    Text(
        modifier = modifier,
        text = "Загрузка...",
        style = AppTheme.typography.bodyMedium
    )
}

@Composable
private fun ErrorContent(
    modifier: Modifier = Modifier,
    error: Throwable
) {
    Text(
        modifier = modifier,
        text = "Ошибка: ${error.message}",
        style = AppTheme.typography.bodyMedium
    )
}

@PreviewLightDark
@Composable
private fun TaskAttachmentListContentPreview(
    @PreviewParameter(StatePreviewProvider::class)
    state: TaskAttachmentListState
) = PreviewSandbox {
    TaskAttachmentListContent(
        modifier = Modifier.fillMaxSize(),
        component = injectPreviewComponent(state) {
            parametersOf(TaskAttachmentsScreenFeature.Params(TaskId("")))
        }
    )
}

private class StatePreviewProvider : PreviewParameterProvider<TaskAttachmentListState> {
    override val values = sequenceOf(
        TaskAttachmentListState.Content(
            items = persistentListOf(
                TaskAttachmentListItem(
                    id = TaskAttachmentId("1"),
                    filename = "IMG_3932.jpeg",
                    type = TaskAttachmentType.Image,
                    size = 1024 * 1024,
                    localPath = "/path/to/image.jpg"
                ),
                TaskAttachmentListItem(
                    id = TaskAttachmentId("2"),
                    filename = "document.pdf",
                    type = TaskAttachmentType.Document,
                    size = 2 * 1024 * 1024,
                    localPath = "/path/to/document.pdf"
                )
            )
        ),
        TaskAttachmentListState.Loading
    )
}
