plugins {
    id(libs.plugins.notemess.library.asProvider().get().pluginId)
    id(libs.plugins.notemess.library.compose.get().pluginId)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "com.notemess.feature.task.attachment"
}

kotlin {
    explicitApi()
}

dependencies {
    api(projects.feature.task.attachment.attachmentApi)
    api(libs.koin.core)

    implementation(projects.core.data)
    implementation(projects.core.ui)

    implementation(projects.feature.account.accountApi)
    implementation(projects.feature.task.attachment.attachmentDb)
    implementation(projects.feature.task.common)
    implementation(projects.system.fileApi)

    implementation(libs.kotlinx.collections.immutable)

    implementation(libs.koin.compose)

    implementation(libs.decompose.extensions.compose)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.firestore)
}
