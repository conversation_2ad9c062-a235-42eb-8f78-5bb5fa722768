package com.notemess.feature.task.presentation

import com.arkivanov.decompose.ComponentContext
import com.domain.resource.StringManager
import com.notemess.core.ui.decompose.componentScope
import com.notemess.core.ui.decompose.host
import com.notemess.core.ui.preview.previewDataOrDefault
import com.notemess.feature.task.TaskScreen
import com.notemess.feature.task.common.TaskId
import com.notemess.feature.task.common.edit.TaskEditManager
import com.notemess.feature.task.domain.usecase.CreateNewTaskFromDraftUseCase
import com.notemess.feature.task.presentation.child.TaskScreenChildAttachmentsEditorFactory
import com.notemess.feature.task.presentation.child.TaskScreenChildDescriptionEditorFactory
import com.notemess.feature.task.presentation.child.TaskScreenChildInfoFactory
import com.notemess.feature.task.presentation.child.TaskScreenChildMemberListFactory
import com.notemess.feature.task.presentation.topbar.TaskScreenTopBarAction
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

@Suppress("LongParameterList")
internal class TaskScreenComponentImpl(
    context: ComponentContext,
    private val params: TaskScreen.Params,
    private val stringManager: StringManager,
    private val taskEditManager: TaskEditManager,
    private val createNewTaskFromDraftUseCase: CreateNewTaskFromDraftUseCase,
) : ComponentContext by context,
    TaskScreenComponent {

    private val mutableState = MutableStateFlow(context.previewDataOrDefault(TaskScreenState.Default))

    private val host = host<Configuration, TaskScreenComponent.Child> {
        handleBackButton = true
        initialConfiguration = Configuration.Info(taskId = params.taskId, isNewTask = params.isNewTask)
        serializer = Configuration.serializer()

        registerChildFactory(TaskScreenChildInfoFactory(mutableState, stringManager))
        registerChildFactory(TaskScreenChildDescriptionEditorFactory(mutableState, stringManager))
        registerChildFactory(TaskScreenChildMemberListFactory(mutableState, stringManager))
        registerChildFactory(TaskScreenChildAttachmentsEditorFactory(mutableState, stringManager))

        onActiveConfigurationChanged { onActiveConfigurationChanged() }
    }

    override val type: TaskScreenType = if (params.isNewTask) {
        TaskScreenType.Modal
    } else {
        TaskScreenType.FullScreen
    }

    override val state = mutableState.asStateFlow()

    override val childStack = host.childStack

    private fun onActiveConfigurationChanged() {
        mutableState.update { state ->
            state.copy(
                isBackAvailable = type == TaskScreenType.FullScreen ||
                        childStack.value.items.size > 1
            )
        }
    }

    override fun onDismissRequest() {
        host.navigateBack()
    }

    override fun onBackClicked() {
        host.navigateBack()
    }

    override fun onCloseClicked() {
        host.navigateBack()
    }

    override fun onActionClicked() {
        val action = mutableState.value.action
        val activeConfiguration = childStack.value.active.configuration

        when {
            action == TaskScreenTopBarAction.Create &&
                    activeConfiguration is Configuration.Info &&
                    params.isNewTask -> {
                componentScope.launch {
                    createNewTaskFromDraftUseCase(params.taskId)
                        .onSuccess { host.navigateBack() }
                }
            }

            action == TaskScreenTopBarAction.Done -> when (activeConfiguration) {
                is Configuration.DescriptionEditor,
                is Configuration.MemberList,
                is Configuration.AttachmentsEditor -> {
                    componentScope.launch {
                        taskEditManager.commitAllSessions(taskId = params.taskId)
                            .onSuccess { host.navigateBack() }
                    }
                }

                else -> {}
            }
        }
    }

    @Serializable
    sealed interface Configuration {
        data class Info(val taskId: TaskId, val isNewTask: Boolean) : Configuration
        data class DescriptionEditor(val taskId: TaskId) : Configuration
        data class MemberList(val taskId: TaskId) : Configuration
        data class AttachmentsEditor(val taskId: TaskId) : Configuration
    }
}
